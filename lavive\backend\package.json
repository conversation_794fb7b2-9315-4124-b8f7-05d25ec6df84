{"name": "lavive-backend", "version": "1.0.0", "description": "Lavive Delivery Management System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm install"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "dotenv": "^16.3.1", "firebase-admin": "^11.10.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.1"}}