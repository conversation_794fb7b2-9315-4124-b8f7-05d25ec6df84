// Main application controller

class App {
    constructor() {
        this.initialized = false;
        this.refreshInterval = null;
    }

    // Initialize the application
    async init() {
        try {
            console.log('Initializing Commed Delivery Management System...');
            
            // Show loading spinner
            UI.showLoading();

            // Initialize authentication from storage
            Auth.initializeFromStorage();

            // Set up event listeners
            this.setupEventListeners();

            // Check API health
            const isHealthy = await API.healthCheck();
            if (!isHealthy) {
                console.warn('API health check failed');
                UI.showToast('Backend server may be unavailable', 'warning');
            }

            this.initialized = true;
            console.log('Application initialized successfully');

        } catch (error) {
            console.error('Application initialization failed:', error);
            UI.showToast('Application initialization failed', 'error');
        } finally {
            UI.hideLoading();
        }
    }

    // Set up event listeners
    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }

        // Navigation buttons
        const newOrderBtn = document.getElementById('new-order-btn');
        if (newOrderBtn) {
            newOrderBtn.addEventListener('click', () => UI.showOrderForm());
        }

        const backToDashboard = document.getElementById('back-to-dashboard');
        if (backToDashboard) {
            backToDashboard.addEventListener('click', () => this.showDashboard());
        }

        const backFromDetail = document.getElementById('back-from-detail');
        if (backFromDetail) {
            backFromDetail.addEventListener('click', () => this.showDashboard());
        }

        // Order form
        const orderForm = document.getElementById('order-form');
        if (orderForm) {
            orderForm.addEventListener('submit', this.handleOrderSubmit.bind(this));
        }

        const cancelForm = document.getElementById('cancel-form');
        if (cancelForm) {
            cancelForm.addEventListener('click', () => this.showDashboard());
        }

        // Edit order button
        const editOrderBtn = document.getElementById('edit-order-btn');
        if (editOrderBtn) {
            editOrderBtn.addEventListener('click', () => {
                if (UI.currentOrder) {
                    UI.showOrderForm(UI.currentOrder);
                }
            });
        }

        // Search and filter
        const searchInput = document.getElementById('search-orders');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => UI.filterOrders(), 300));
        }

        const filterSelect = document.getElementById('filter-status');
        if (filterSelect) {
            filterSelect.addEventListener('change', () => UI.filterOrders());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }

    // Handle login form submission
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;

        // Clear previous errors
        UI.hideError('login-error');

        // Validate input
        if (!email || !password) {
            UI.showError('login-error', 'Please enter both email and password');
            return;
        }

        try {
            await Auth.signIn(email, password);
            // Success is handled by auth state change listener
        } catch (error) {
            // Error is handled by Auth.handleAuthError
        }
    }

    // Handle logout
    async handleLogout() {
        try {
            await Auth.signOut();
            this.clearRefreshInterval();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    // Handle order form submission
    async handleOrderSubmit(event) {
        event.preventDefault();

        const formData = UI.getOrderFormData();
        
        // Validate form data
        const errors = UI.validateOrderForm(formData);
        if (errors.length > 0) {
            UI.showToast(errors.join(', '), 'error');
            return;
        }

        try {
            UI.showLoading();

            let response;
            if (UI.isEditing && UI.currentOrder) {
                // Update existing order
                response = await API.updateOrder(UI.currentOrder.id, formData);
                UI.showToast('Order updated successfully!', 'success');
            } else {
                // Create new order
                response = await API.createOrder(formData);
                UI.showToast('Order created successfully!', 'success');
            }

            // Refresh dashboard and show it
            await this.loadDashboardData();
            this.showDashboard();

        } catch (error) {
            console.error('Error saving order:', error);
            UI.showToast('Failed to save order', 'error');
        } finally {
            UI.hideLoading();
        }
    }

    // Initialize dashboard
    async initializeDashboard() {
        if (!Auth.isAuthenticated()) {
            return;
        }

        try {
            await this.loadDashboardData();
            this.setupAutoRefresh();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            UI.showToast('Failed to load dashboard data', 'error');
        }
    }

    // Load dashboard data
    async loadDashboardData() {
        try {
            // Load orders and stats in parallel
            const [ordersResponse] = await Promise.all([
                API.getOrders({ limit: 50 })
            ]);

            if (ordersResponse.success) {
                const orders = ordersResponse.data;
                UI.renderOrdersTable(orders);
                
                // Calculate stats from orders
                const stats = this.calculateStats(orders);
                UI.updateStats(stats);
            }

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            throw error;
        }
    }

    // Calculate statistics from orders
    calculateStats(orders) {
        const stats = {
            total: orders.length,
            byStatus: {}
        };

        CONFIG.APP.ORDER_STATUSES.forEach(status => {
            stats.byStatus[status] = 0;
        });

        orders.forEach(order => {
            if (stats.byStatus.hasOwnProperty(order.status)) {
                stats.byStatus[order.status]++;
            }
        });

        return stats;
    }

    // Show dashboard view
    showDashboard() {
        if (!Auth.isAuthenticated()) {
            console.warn('Cannot show dashboard: user not authenticated');
            return;
        }

        UI.showView('dashboard');
        this.loadDashboardData();
    }

    // Set up auto-refresh
    setupAutoRefresh() {
        this.clearRefreshInterval();
        
        this.refreshInterval = setInterval(async () => {
            if (UI.currentView === 'dashboard' && Auth.isAuthenticated()) {
                try {
                    await this.loadDashboardData();
                } catch (error) {
                    console.error('Auto-refresh failed:', error);
                }
            }
        }, CONFIG.APP.AUTO_REFRESH_INTERVAL);
    }

    // Clear refresh interval
    clearRefreshInterval() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + N: New order
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            if (Auth.isAuthenticated() && UI.currentView === 'dashboard') {
                UI.showOrderForm();
            }
        }

        // Escape: Go back/cancel
        if (event.key === 'Escape') {
            if (UI.currentView !== 'dashboard') {
                this.showDashboard();
            }
        }
    }

    // Utility: Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Handle application errors
    handleError(error, context = '') {
        console.error(`Application error ${context}:`, error);
        
        let message = 'An unexpected error occurred';
        if (error.message) {
            message = error.message;
        }
        
        UI.showToast(message, 'error');
    }

    // Cleanup on page unload
    cleanup() {
        this.clearRefreshInterval();
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    window.App = new App();
    await window.App.init();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.App) {
        window.App.cleanup();
    }
});

// Handle uncaught errors
window.addEventListener('error', (event) => {
    console.error('Uncaught error:', event.error);
    if (window.App) {
        window.App.handleError(event.error, 'uncaught');
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.App) {
        window.App.handleError(event.reason, 'unhandled promise');
    }
});
