const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const initializeFirebase = () => {
  if (admin.apps.length === 0) {
    // Check if Firebase credentials are properly configured
    const requiredEnvVars = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_PRIVATE_KEY',
      'FIREBASE_CLIENT_EMAIL'
    ];

    const missingVars = requiredEnvVars.filter(varName =>
      !process.env[varName] || process.env[varName].includes('placeholder')
    );

    if (missingVars.length > 0) {
      console.warn('⚠️  Firebase not configured properly. Missing or placeholder values for:', missingVars.join(', '));
      console.warn('⚠️  Server will start but Firebase features will not work.');
      console.warn('⚠️  Please follow the setup guide in docs/SETUP.md to configure Firebase.');

      // Return a mock admin object to prevent crashes
      return {
        auth: () => ({
          verifyIdToken: () => Promise.reject(new Error('Firebase not configured')),
        }),
        firestore: () => ({
          collection: () => ({
            add: () => Promise.reject(new Error('Firebase not configured')),
            get: () => Promise.reject(new Error('Firebase not configured')),
            doc: () => ({
              get: () => Promise.reject(new Error('Firebase not configured')),
              update: () => Promise.reject(new Error('Firebase not configured')),
            })
          })
        })
      };
    }

    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: process.env.FIREBASE_AUTH_URI,
      token_uri: process.env.FIREBASE_TOKEN_URI,
      auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
      client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    console.log('✅ Firebase Admin SDK initialized successfully');
  }

  return admin;
};

// Initialize and export
const firebaseAdmin = initializeFirebase();

module.exports = firebaseAdmin;
