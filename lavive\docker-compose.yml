version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lavive-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - FRONTEND_URL=http://localhost:8080
      # Add your Firebase environment variables here
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID}
      - FIREBASE_AUTH_URI=${FIREBASE_AUTH_URI}
      - FIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI}
      - FIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL}
      - FIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./backend:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - lavive-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: lavive-frontend
    ports:
      - "8080:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - lavive-network

networks:
  lavive-network:
    driver: bridge

volumes:
  node_modules:
