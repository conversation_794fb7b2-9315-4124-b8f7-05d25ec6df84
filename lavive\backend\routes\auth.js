const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');

// POST /api/auth/verify - Verify Firebase token
router.post('/verify', authenticateToken, (req, res) => {
  // If we reach here, the token is valid (middleware passed)
  res.json({
    success: true,
    user: {
      uid: req.user.uid,
      email: req.user.email,
      name: req.user.name || req.user.email
    },
    message: 'Token is valid'
  });
});

// POST /api/auth/refresh - Refresh user session
router.post('/refresh', authenticateToken, (req, res) => {
  // Return updated user info
  res.json({
    success: true,
    user: {
      uid: req.user.uid,
      email: req.user.email,
      name: req.user.name || req.user.email,
      lastLogin: new Date().toISOString()
    }
  });
});

// GET /api/auth/profile - Get user profile
router.get('/profile', authenticateToken, (req, res) => {
  res.json({
    success: true,
    user: {
      uid: req.user.uid,
      email: req.user.email,
      name: req.user.name || req.user.email,
      emailVerified: req.user.email_verified || false
    }
  });
});

module.exports = router;
