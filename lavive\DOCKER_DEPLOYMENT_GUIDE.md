# Docker Desktop Deployment Guide for Lavive

This guide will help you deploy your Lavive Delivery Management System using Docker Desktop.

## Prerequisites

1. **Docker Desktop**: Download and install from [https://www.docker.com/products/docker-desktop](https://www.docker.com/products/docker-desktop)
2. **Git** (optional): For version control
3. **Text Editor**: VS Code, Notepad++, or any text editor

## Project Structure

```
lavive/
├── backend/
│   ├── Dockerfile
│   ├── package.json
│   ├── server.js
│   └── ... (other backend files)
├── frontend/
│   ├── Dockerfile
│   ├── nginx.conf
│   ├── index.html
│   └── ... (other frontend files)
├── docker-compose.yml
├── .env.example
└── .dockerignore
```

## Step 1: Setup Environment Variables

1. Copy the `.env.example` file to `.env`:
   ```bash
   copy .env.example .env
   ```

2. Edit the `.env` file with your actual Firebase credentials:
   - Open `.env` in a text editor
   - Replace `YOUR_PRIVATE_KEY_HERE` with your actual Firebase private key
   - Update other placeholder values as needed

## Step 2: Install Dependencies (First Time Only)

Navigate to the backend directory and install dependencies:

```bash
cd lavive/backend
npm install
cd ..
```

## Step 3: Build and Run with Docker Compose

1. **Open Command Prompt/PowerShell** in the `lavive` directory

2. **Build and start all services**:
   ```bash
   docker-compose up --build
   ```

   Or run in detached mode (background):
   ```bash
   docker-compose up --build -d
   ```

3. **Wait for the build process** to complete. You should see output like:
   ```
   lavive-backend  | Server running on port 3000
   lavive-frontend | /docker-entrypoint.sh: Configuration complete; ready for start up
   ```

## Step 4: Access Your Application

- **Frontend**: Open your browser and go to [http://localhost:8080](http://localhost:8080)
- **Backend API**: Available at [http://localhost:3000](http://localhost:3000)
- **Health Check**: [http://localhost:3000/health](http://localhost:3000/health)

## Docker Commands Reference

### Basic Operations
```bash
# Start services
docker-compose up

# Start services in background
docker-compose up -d

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build

# View logs
docker-compose logs

# View logs for specific service
docker-compose logs backend
docker-compose logs frontend
```

### Development Commands
```bash
# Restart a specific service
docker-compose restart backend

# Execute commands in running container
docker-compose exec backend npm install new-package

# View running containers
docker ps

# Remove all containers and images (clean slate)
docker-compose down --rmi all --volumes
```

## Step 5: Verify Deployment

1. **Check Backend Health**:
   - Visit [http://localhost:3000/health](http://localhost:3000/health)
   - Should return JSON with status "OK"

2. **Check Frontend**:
   - Visit [http://localhost:8080](http://localhost:8080)
   - Should load the Lavive application

3. **Check Docker Desktop**:
   - Open Docker Desktop
   - Go to "Containers" tab
   - You should see `lavive-backend` and `lavive-frontend` running

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```
   Error: Port 3000 is already in use
   ```
   **Solution**: Stop other services using those ports or change ports in `docker-compose.yml`

2. **Firebase Configuration Error**:
   ```
   Error: Firebase configuration missing
   ```
   **Solution**: Check your `.env` file and ensure all Firebase variables are set correctly

3. **Build Fails**:
   ```
   Error: npm install failed
   ```
   **Solution**: Delete `node_modules` and try again:
   ```bash
   docker-compose down
   docker-compose up --build --force-recreate
   ```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# View specific service logs
docker-compose logs backend
docker-compose logs frontend

# Access container shell
docker-compose exec backend sh
docker-compose exec frontend sh
```

## Production Deployment

For production deployment, consider:

1. **Environment Variables**: Use production values in `.env`
2. **SSL/HTTPS**: Configure reverse proxy (nginx, Traefik)
3. **Domain Names**: Update CORS settings and frontend API URLs
4. **Monitoring**: Add logging and monitoring solutions
5. **Backup**: Implement backup strategies for data

## Stopping the Application

```bash
# Stop services (keeps containers)
docker-compose stop

# Stop and remove containers
docker-compose down

# Stop and remove everything (containers, networks, volumes)
docker-compose down --volumes --remove-orphans
```

## Next Steps

1. **Configure your frontend** to point to the correct backend URL
2. **Set up your Firebase project** with proper security rules
3. **Test all functionality** (authentication, orders, etc.)
4. **Consider production deployment** options (AWS, Google Cloud, etc.)

## Support

If you encounter issues:
1. Check the logs using `docker-compose logs`
2. Verify your `.env` file configuration
3. Ensure Docker Desktop is running
4. Check that ports 3000 and 8080 are available
