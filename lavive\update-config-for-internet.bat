@echo off
echo ========================================
echo    Update Frontend Config for Internet
echo ========================================
echo.

set /p backend_url="Enter your backend URL (e.g., https://your-app.railway.app): "

if "%backend_url%"=="" (
    echo Error: Backend URL is required
    pause
    exit /b 1
)

echo.
echo Updating frontend configuration...

REM Create a temporary config file
echo // Configuration file for the Commed Delivery Management System > temp_config.js
echo // Updated for internet deployment >> temp_config.js
echo console.log('🚀 CONFIG FILE LOADED - Internet Version'); >> temp_config.js
echo. >> temp_config.js
echo // API Configuration >> temp_config.js
echo const API_CONFIG = { >> temp_config.js
echo     BASE_URL: (() =^> { >> temp_config.js
echo         const hostname = window.location.hostname; >> temp_config.js
echo         console.log('🔧 CONFIG: Current hostname:', hostname); >> temp_config.js
echo         console.log('🔧 CONFIG: Location:', window.location.href); >> temp_config.js
echo. >> temp_config.js
echo         if (hostname === 'localhost' ^|^| hostname === '127.0.0.1') { >> temp_config.js
echo             console.log('🔧 CONFIG: Using local backend'); >> temp_config.js
echo             return 'http://localhost:3000/api'; >> temp_config.js
echo         } else { >> temp_config.js
echo             console.log('🔧 CONFIG: Using production backend'); >> temp_config.js
echo             const productionURL = '%backend_url%/api'; >> temp_config.js
echo             console.log('🔧 CONFIG: Production URL:', productionURL); >> temp_config.js
echo             return productionURL; >> temp_config.js
echo         } >> temp_config.js
echo     })(), >> temp_config.js
echo     TIMEOUT: 10000, // 10 seconds >> temp_config.js
echo     RETRY_ATTEMPTS: 3 >> temp_config.js
echo }; >> temp_config.js

REM Copy the rest of the original config
echo. >> temp_config.js
echo // Log the final configuration >> temp_config.js
echo console.log('🔧 CONFIG: Final API_CONFIG:', API_CONFIG); >> temp_config.js

REM Add Firebase and other configs (copy from original)
type frontend\js\config.js | findstr /V "BASE_URL" | findstr /V "productionURL" | findstr /V "return productionURL" >> temp_config.js

REM Replace the original config
move temp_config.js frontend\js\config.js

echo.
echo ✅ Frontend configuration updated!
echo Backend URL set to: %backend_url%
echo.
echo Don't forget to:
echo 1. Update CORS settings in your backend
echo 2. Set FRONTEND_URL environment variable in your backend
echo.
pause
