{"name": "cache-base", "description": "Basic object cache with `get`, `set`, `del`, and `has` methods for node.js/javascript projects.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/cache-base", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "(https://github.com/wtgtybhertgeghgtwtg)"], "repository": "jonschlinkert/cache-base", "bugs": {"url": "https://github.com/jonschlinkert/cache-base/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "keywords": ["base", "cache", "config", "data", "get", "has", "hash", "hasown", "object", "set", "store"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"highligh": "base", "list": ["base-methods", "get-value", "has-value", "option-cache", "set-value", "unset-value"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}