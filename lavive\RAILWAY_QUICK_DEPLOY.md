# 🚂 Railway Quick Deploy Guide

Railway is the easiest way to deploy your Lavive app to the internet. It's free to start and works great with <PERSON><PERSON>.

## 📋 Prerequisites

1. **GitHub Account** - [Sign up here](https://github.com)
2. **Railway Account** - [Sign up here](https://railway.app)

## 🚀 Step-by-Step Deployment

### Step 1: Push to GitHub

1. **Create a new repository** on GitHub:
   - Go to [github.com/new](https://github.com/new)
   - Name it `lavive` or `lavive-delivery`
   - Make it public (for free Railway deployment)

2. **Push your code** (run in your lavive directory):
   ```bash
   git init
   git add .
   git commit -m "Initial commit - Lavive delivery app"
   git branch -M main
   git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO_NAME.git
   git push -u origin main
   ```

### Step 2: Deploy Backend to Railway

1. **Go to Railway**: [railway.app](https://railway.app)
2. **Sign up** with your GitHub account
3. **Create New Project** → **Deploy from GitHub repo**
4. **Select your repository**
5. **Choose the backend folder** when prompted
6. **Wait for deployment** (Railway will detect your Dockerfile automatically)

### Step 3: Configure Environment Variables

1. **In Railway dashboard**, click on your backend service
2. **Go to Variables tab**
3. **Add these variables** (copy from your `.env` file):
   ```
   NODE_ENV=production
   FIREBASE_PROJECT_ID=lavive-cb219
   FIREBASE_PRIVATE_KEY_ID=your-key-id
   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-key\n-----END PRIVATE KEY-----\n"
   FIREBASE_CLIENT_EMAIL=<EMAIL>
   FIREBASE_CLIENT_ID=your-client-id
   FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
   FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
   FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
   FIREBASE_CLIENT_X509_CERT_URL=your-cert-url
   JWT_SECRET=your-super-secret-jwt-key
   ```

### Step 4: Deploy Frontend to Railway

1. **In Railway dashboard**, click **New Service**
2. **Deploy from GitHub repo** → Select same repository
3. **Choose the frontend folder**
4. **Wait for deployment**

### Step 5: Update Configuration

1. **Get your backend URL** from Railway (looks like `https://backend-production-xxxx.up.railway.app`)
2. **Update frontend config**:
   - Run `update-config-for-internet.bat`
   - Enter your backend URL when prompted
3. **Set FRONTEND_URL** in backend environment variables:
   - Go to backend service in Railway
   - Add variable: `FRONTEND_URL=https://frontend-production-xxxx.up.railway.app`

### Step 6: Redeploy Frontend

1. **Commit the config changes**:
   ```bash
   git add frontend/js/config.js
   git commit -m "Update config for production"
   git push
   ```
2. **Railway will automatically redeploy** your frontend

## 🎉 Your App is Live!

- **Frontend**: `https://frontend-production-xxxx.up.railway.app`
- **Backend**: `https://backend-production-xxxx.up.railway.app`
- **Health Check**: `https://backend-production-xxxx.up.railway.app/health`

## 💰 Pricing

- **Free Tier**: $5 credit per month (enough for small apps)
- **Pro Plan**: $20/month for unlimited usage
- **Pay-as-you-go**: Only pay for what you use

## 🔧 Troubleshooting

### Backend Won't Start
- Check logs in Railway dashboard
- Verify all environment variables are set
- Make sure Firebase credentials are correct

### Frontend Can't Connect to Backend
- Verify backend URL in frontend config
- Check CORS settings in backend
- Ensure FRONTEND_URL is set in backend environment

### Build Failures
- Check Dockerfile syntax
- Verify package.json dependencies
- Look at build logs in Railway dashboard

## 🚀 Next Steps

1. **Custom Domain**: Add your own domain in Railway settings
2. **SSL Certificate**: Automatic with Railway
3. **Monitoring**: Use Railway's built-in monitoring
4. **Scaling**: Railway auto-scales based on traffic

## 📞 Need Help?

- Railway Documentation: [docs.railway.app](https://docs.railway.app)
- Railway Discord: [discord.gg/railway](https://discord.gg/railway)
- Check the logs in Railway dashboard for error details
