const admin = require('../config/firebase');
const db = admin.firestore();

const COLLECTION_NAME = 'orders';

/**
 * Order status options
 */
const ORDER_STATUSES = [
  'New',
  'Processing', 
  'Shipped',
  'Delivered',
  'Cancelled'
];

/**
 * Create a new order
 */
const createOrder = async (orderData) => {
  try {
    const timestamp = admin.firestore.FieldValue.serverTimestamp();
    
    const order = {
      ...orderData,
      createdAt: timestamp,
      updatedAt: timestamp,
      deleted: false
    };

    const docRef = await db.collection(COLLECTION_NAME).add(order);
    console.log('Order created with ID:', docRef.id);
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

/**
 * Get all orders with optional filtering and pagination
 */
const getAllOrders = async (options = {}) => {
  try {
    console.log('getAllOrders called with options:', options);
    const { page = 1, limit = 20, status, search } = options;

    // Start with a simple query first
    console.log('Querying collection:', COLLECTION_NAME);
    let query = db.collection(COLLECTION_NAME);

    // Check if collection exists and has documents
    const collectionSnapshot = await query.limit(1).get();
    console.log('Collection exists:', !collectionSnapshot.empty);
    console.log('Collection size:', collectionSnapshot.size);

    // Build the query step by step
    query = db.collection(COLLECTION_NAME);

    // Add deleted filter if documents exist
    if (!collectionSnapshot.empty) {
      query = query.where('deleted', '==', false);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.limit(limit);

    // Try to order by createdAt if possible
    try {
      if (!collectionSnapshot.empty) {
        query = query.orderBy('createdAt', 'desc');
      }
    } catch (orderError) {
      console.warn('Could not order by createdAt:', orderError.message);
    }

    console.log('Executing query...');
    const snapshot = await query.get();
    console.log('Query executed, found documents:', snapshot.size);

    let orders = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      console.log('Processing document:', doc.id, data);
      orders.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.() || data.updatedAt
      });
    });

    // Filter by status if provided
    if (status && ORDER_STATUSES.includes(status)) {
      orders = orders.filter(order => order.status === status);
    }

    // Apply search filter if provided (client-side for simplicity)
    if (search) {
      const searchLower = search.toLowerCase();
      orders = orders.filter(order =>
        order.customerName?.toLowerCase().includes(searchLower) ||
        order.customerPhone?.includes(search) ||
        order.articleModel?.toLowerCase().includes(searchLower) ||
        order.deliveryAddress?.toLowerCase().includes(searchLower)
      );
    }

    console.log('Returning orders:', orders.length);
    return orders;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

/**
 * Get order by ID
 */
const getOrderById = async (orderId) => {
  try {
    const doc = await db.collection(COLLECTION_NAME).doc(orderId).get();
    
    if (!doc.exists || doc.data().deleted) {
      return null;
    }

    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate?.() || data.createdAt,
      updatedAt: data.updatedAt?.toDate?.() || data.updatedAt
    };
  } catch (error) {
    console.error('Error fetching order:', error);
    throw error;
  }
};

/**
 * Update an order
 */
const updateOrder = async (orderId, updateData) => {
  try {
    const docRef = db.collection(COLLECTION_NAME).doc(orderId);
    
    // Check if order exists and is not deleted
    const doc = await docRef.get();
    if (!doc.exists || doc.data().deleted) {
      return false;
    }

    const updatedOrder = {
      ...updateData,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await docRef.update(updatedOrder);
    console.log('Order updated:', orderId);
    
    return true;
  } catch (error) {
    console.error('Error updating order:', error);
    throw error;
  }
};

/**
 * Soft delete an order
 */
const deleteOrder = async (orderId, deletedBy) => {
  try {
    const docRef = db.collection(COLLECTION_NAME).doc(orderId);
    
    // Check if order exists
    const doc = await docRef.get();
    if (!doc.exists) {
      return false;
    }

    await docRef.update({
      deleted: true,
      deletedAt: admin.firestore.FieldValue.serverTimestamp(),
      deletedBy: deletedBy,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    console.log('Order soft deleted:', orderId);
    return true;
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

/**
 * Get order statistics
 */
const getOrderStats = async () => {
  try {
    const snapshot = await db.collection(COLLECTION_NAME)
      .where('deleted', '==', false)
      .get();

    const stats = {
      total: 0,
      byStatus: {}
    };

    ORDER_STATUSES.forEach(status => {
      stats.byStatus[status] = 0;
    });

    snapshot.forEach(doc => {
      const data = doc.data();
      stats.total++;
      if (data.status && stats.byStatus.hasOwnProperty(data.status)) {
        stats.byStatus[data.status]++;
      }
    });

    return stats;
  } catch (error) {
    console.error('Error fetching order stats:', error);
    throw error;
  }
};

module.exports = {
  createOrder,
  getAllOrders,
  getOrderById,
  updateOrder,
  deleteOrder,
  getOrderStats,
  ORDER_STATUSES
};
