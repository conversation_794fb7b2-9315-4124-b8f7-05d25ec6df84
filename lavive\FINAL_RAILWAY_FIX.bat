@echo off
echo ========================================
echo    FINAL Railway Fix - Restructure Repository
echo ========================================
echo.

echo Current structure (what <PERSON> sees):
echo ./
echo ├── lavive/          ^<-- Your app is here
echo │   ├── backend/
echo │   └── frontend/
echo └── .gitattributes   ^<-- Railway starts here
echo.

echo We need to move files so Railway sees:
echo ./
echo ├── backend/         ^<-- Railway will find this
echo ├── frontend/
echo ├── package.json
echo └── docker-compose.yml
echo.

set /p confirm="Continue with repository restructure? (y/n): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Step 1: Navigate to repository root...
cd ..

echo Step 2: Moving backend files...
if exist "lqviv\lavive\backend" (
    move "lqviv\lavive\backend" "lqviv\backend"
    echo ✅ Backend moved
) else (
    echo ❌ Backend directory not found
)

echo Step 3: Moving frontend files...
if exist "lqviv\lavive\frontend" (
    move "lqviv\lavive\frontend" "lqviv\frontend"
    echo ✅ Frontend moved
) else (
    echo ❌ Frontend directory not found
)

echo Step 4: Moving configuration files...
if exist "lqviv\lavive\docker-compose.yml" move "lqviv\lavive\docker-compose.yml" "lqviv\"
if exist "lqviv\lavive\package.json" move "lqviv\lavive\package.json" "lqviv\"
if exist "lqviv\lavive\*.md" move "lqviv\lavive\*.md" "lqviv\"
if exist "lqviv\lavive\*.bat" move "lqviv\lavive\*.bat" "lqviv\"
if exist "lqviv\lavive\*.sh" move "lqviv\lavive\*.sh" "lqviv\"
if exist "lqviv\lavive\.env*" move "lqviv\lavive\.env*" "lqviv\"
if exist "lqviv\lavive\.docker*" move "lqviv\lavive\.docker*" "lqviv\"
if exist "lqviv\lavive\railway.json" move "lqviv\lavive\railway.json" "lqviv\"
if exist "lqviv\lavive\nixpacks.toml" move "lqviv\lavive\nixpacks.toml" "lqviv\"

echo Step 5: Cleaning up empty lavive directory...
if exist "lqviv\lavive" (
    rmdir "lqviv\lavive" /S /Q 2>nul
    echo ✅ Empty lavive directory removed
)

echo Step 6: Going back to project directory...
cd lqviv

echo.
echo ========================================
echo    Repository Restructured!
echo ========================================
echo.

echo New structure:
dir /B

echo.
echo Step 7: Committing changes to Git...
git add .
git commit -m "Restructure for Railway: move app files to repository root"

echo Step 8: Pushing to GitHub...
git push

echo.
echo ========================================
echo    SUCCESS! Repository Fixed
echo ========================================
echo.
echo Now Railway will see:
echo ├── backend/         ^<-- Your Node.js app
echo ├── frontend/        ^<-- Your frontend
echo ├── package.json     ^<-- Dependencies
echo └── docker-compose.yml
echo.
echo Next steps:
echo 1. Go to Railway dashboard
echo 2. Delete the old failed service
echo 3. Create NEW service from GitHub repo
echo 4. Railway should now detect Node.js app automatically
echo 5. Set root directory to 'backend' if needed
echo.
pause
