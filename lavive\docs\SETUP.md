# Commed Delivery Management System - Setup Guide

## Prerequisites

Before setting up the application, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **Git** - [Download here](https://git-scm.com/)
- **Firebase Account** - [Create account](https://firebase.google.com/)

## Firebase Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `commed-delivery-system`
4. Enable Google Analytics (optional)
5. Create project

### 2. Enable Authentication

1. In Firebase Console, go to **Authentication**
2. Click **Get started**
3. Go to **Sign-in method** tab
4. Enable **Email/Password** provider
5. Click **Save**

### 3. Create Firestore Database

1. Go to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (we'll secure it later)
4. Select your preferred location
5. Click **Done**

### 4. Generate Service Account Key

1. Go to **Project Settings** (gear icon)
2. Click **Service accounts** tab
3. Click **Generate new private key**
4. Download the JSON file
5. Keep this file secure - you'll need it for backend configuration

### 5. Get Web App Configuration

1. In **Project Settings**, go to **General** tab
2. Scroll to **Your apps** section
3. Click **Web app** icon (`</>`)
4. Register app with name: `commed-delivery-frontend`
5. Copy the configuration object - you'll need this for frontend

## Backend Setup

### 1. Install Dependencies

```bash
cd backend
npm install
```

### 2. Environment Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your Firebase service account details:
   ```env
   PORT=3000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:8080

   # Firebase Configuration (from service account JSON)
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_PRIVATE_KEY_ID=your-private-key-id
   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
   FIREBASE_CLIENT_EMAIL=your-service-account-email
   FIREBASE_CLIENT_ID=your-client-id
   FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
   FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
   FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
   FIREBASE_CLIENT_X509_CERT_URL=your-cert-url
   ```

### 3. Start Backend Server

```bash
npm run dev
```

The backend will start on `http://localhost:3000`

## Frontend Setup

### 1. Install Dependencies

```bash
cd frontend
npm install
```

### 2. Configure Firebase

Edit `frontend/js/config.js` and update the `FIREBASE_CONFIG` object with your web app configuration:

```javascript
const FIREBASE_CONFIG = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "*********",
    appId: "your-app-id"
};
```

### 3. Start Frontend Server

```bash
npm start
```

The frontend will start on `http://localhost:8080`

## Create First User

Since we're using Firebase Authentication, you need to create your first user:

### Option 1: Using Firebase Console

1. Go to Firebase Console > Authentication > Users
2. Click **Add user**
3. Enter email and password
4. Click **Add user**

### Option 2: Using the Application

1. Open `http://localhost:8080`
2. Try to sign in with any email/password
3. Firebase will show an error, but you can then go to Firebase Console
4. In Authentication > Users, you'll see the attempted sign-in
5. You can then add the user manually

## Testing the Setup

1. Open `http://localhost:8080` in your browser
2. Sign in with your created user credentials
3. You should see the dashboard
4. Try creating a new order to test the full flow

## Troubleshooting

### Backend Issues

- **Port already in use**: Change `PORT` in `.env` file
- **Firebase connection error**: Check your service account credentials
- **CORS errors**: Ensure `FRONTEND_URL` in `.env` matches your frontend URL

### Frontend Issues

- **Firebase initialization error**: Check your Firebase config in `config.js`
- **API connection error**: Ensure backend is running and `API_CONFIG.BASE_URL` is correct
- **Authentication issues**: Verify Firebase Auth is enabled and configured

### Common Issues

1. **"Firebase project not found"**: Check your project ID in configuration
2. **"Permission denied"**: Ensure Firestore is in test mode or configure security rules
3. **"Network error"**: Check if both frontend and backend servers are running

## Security Notes

- Never commit `.env` files to version control
- Keep your Firebase service account key secure
- Configure Firestore security rules before production deployment
- Use HTTPS in production

## Next Steps

After successful setup:

1. Configure Firestore security rules
2. Set up proper user management
3. Configure deployment for production
4. Set up monitoring and logging
