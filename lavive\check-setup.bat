@echo off
echo ========================================
echo    Lavive Setup Verification
echo ========================================
echo.

echo Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] Docker is not installed or not running
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
) else (
    echo [OK] Docker is installed and running
    docker --version
)

echo.
echo Checking Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [FAIL] Docker Compose is not available
) else (
    echo [OK] Docker Compose is available
    docker-compose --version
)

echo.
echo Checking project files...

if exist "docker-compose.yml" (
    echo [OK] docker-compose.yml found
) else (
    echo [FAIL] docker-compose.yml not found
)

if exist "backend\Dockerfile" (
    echo [OK] Backend Dockerfile found
) else (
    echo [FAIL] Backend Dockerfile not found
)

if exist "frontend\Dockerfile" (
    echo [OK] Frontend Dockerfile found
) else (
    echo [FAIL] Frontend Dockerfile not found
)

if exist ".env" (
    echo [OK] .env file found
) else (
    echo [WARN] .env file not found - will be created from .env.example
)

if exist ".env.example" (
    echo [OK] .env.example found
) else (
    echo [FAIL] .env.example not found
)

echo.
echo Checking backend dependencies...
if exist "backend\package.json" (
    echo [OK] Backend package.json found
) else (
    echo [FAIL] Backend package.json not found
)

echo.
echo Checking frontend files...
if exist "frontend\index.html" (
    echo [OK] Frontend index.html found
) else (
    echo [FAIL] Frontend index.html not found
)

echo.
echo ========================================
echo    Setup Status Summary
echo ========================================
echo.
echo If all checks show [OK], you can run:
echo   deploy.bat
echo.
echo If any checks show [FAIL], please fix those issues first.
echo.
pause
