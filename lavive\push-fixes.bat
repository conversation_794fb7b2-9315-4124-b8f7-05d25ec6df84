@echo off
echo ========================================
echo    Push Railway Fixes to GitHub
echo ========================================
echo.

echo Checking Git status...
git status

echo.
echo Adding updated configuration files...
git add backend/railway.json
git add railway.json
git add backend/package.json
git add RAILWAY_FIX.md

echo.
echo Committing changes...
git commit -m "Fix Railway deployment configuration - update paths and Docker settings"

echo.
echo Pushing to GitHub...
git push

echo.
echo ========================================
echo    Fixes Pushed Successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Go to your Railway dashboard
echo 2. Either redeploy existing service or create new one
echo 3. Make sure Root Directory is set to 'backend'
echo 4. Check RAILWAY_FIX.md for detailed instructions
echo.
pause
