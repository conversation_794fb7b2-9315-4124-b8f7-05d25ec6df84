const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { 
  createOrder, 
  getAllOrders, 
  getOrderById, 
  updateOrder, 
  deleteOrder 
} = require('../models/Order');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// GET /api/orders - Get all orders
router.get('/', async (req, res) => {
  try {
    console.log('GET /api/orders called with query:', req.query);
    console.log('User:', req.user);

    // Test Firestore connection first
    const admin = require('../config/firebase');
    const db = admin.firestore();
    console.log('Testing Firestore connection...');

    // Simple test query
    const testSnapshot = await db.collection('orders').limit(1).get();
    console.log('Firestore test successful, collection size:', testSnapshot.size);

    const { page = 1, limit = 20, status, search } = req.query;

    const orders = await getAllOrders({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search
    });

    console.log('Successfully fetched orders:', orders.length);

    res.json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    console.error('Error stack:', error.stack);
    console.error('Error code:', error.code);
    console.error('Error details:', error.details);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch orders',
      details: error.message,
      code: error.code
    });
  }
});

// GET /api/orders/:id - Get specific order
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const order = await getOrderById(id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order'
    });
  }
});

// POST /api/orders - Create new order
router.post('/', async (req, res) => {
  try {
    const orderData = {
      customerName: req.body.customerName,
      customerPhone: req.body.customerPhone,
      deliveryAddress: req.body.deliveryAddress,
      articleModel: req.body.articleModel,
      quantity: parseInt(req.body.quantity),
      notes: req.body.notes || '',
      status: req.body.status || 'New',
      createdBy: req.user.uid
    };

    // Validate required fields
    const requiredFields = ['customerName', 'customerPhone', 'deliveryAddress', 'articleModel', 'quantity'];
    const missingFields = requiredFields.filter(field => !orderData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      });
    }

    const orderId = await createOrder(orderData);
    
    res.status(201).json({
      success: true,
      data: { id: orderId, ...orderData },
      message: 'Order created successfully'
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create order'
    });
  }
});

// PUT /api/orders/:id - Update order
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user.uid,
      updatedAt: new Date()
    };

    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    const success = await updateOrder(id, updateData);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Order updated successfully'
    });
  } catch (error) {
    console.error('Error updating order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order'
    });
  }
});

// DELETE /api/orders/:id - Delete order (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const success = await deleteOrder(id, req.user.uid);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Order deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete order'
    });
  }
});

module.exports = router;
