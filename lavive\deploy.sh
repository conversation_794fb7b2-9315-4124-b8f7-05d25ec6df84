#!/bin/bash

echo "========================================"
echo "   Lavive Docker Deployment Script"
echo "========================================"
echo

# Check if Docker is running
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker is not installed or not running!"
    echo "Please install Docker Desktop and make sure it's running."
    exit 1
fi

echo "Docker is available!"
echo

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "WARNING: .env file not found!"
    echo
    if [ -f ".env.example" ]; then
        echo "Copying .env.example to .env..."
        cp .env.example .env
        echo
        echo "IMPORTANT: Please edit the .env file with your actual Firebase credentials!"
        echo "Press any key to continue after editing .env file..."
        read -n 1 -s
    else
        echo "ERROR: .env.example file not found!"
        echo "Please create a .env file with your environment variables."
        exit 1
    fi
fi

echo
echo "Starting deployment..."
echo

# Stop any existing containers
echo "Stopping existing containers..."
docker-compose down

echo
echo "Building and starting services..."
docker-compose up --build -d

echo
echo "Waiting for services to start..."
sleep 10

echo
echo "========================================"
echo "   Deployment Status"
echo "========================================"

# Check if containers are running
docker-compose ps

echo
echo "========================================"
echo "   Access Your Application"
echo "========================================"
echo "Frontend: http://localhost:8080"
echo "Backend:  http://localhost:3000"
echo "Health:   http://localhost:3000/health"
echo

# Try to open browser (works on most systems)
if command -v xdg-open &> /dev/null; then
    echo "Opening frontend in browser..."
    xdg-open http://localhost:8080
elif command -v open &> /dev/null; then
    echo "Opening frontend in browser..."
    open http://localhost:8080
fi

echo
echo "Deployment complete!"
echo
echo "To view logs: docker-compose logs"
echo "To stop:      docker-compose down"
echo
