@echo off
echo ========================================
echo    Lavive Docker Deployment Script
echo ========================================
echo.

REM Check if Docker is running
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running!
    echo Please install Docker Desktop and make sure it's running.
    pause
    exit /b 1
)

echo Docker is available!
echo.

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found!
    echo.
    if exist ".env.example" (
        echo Copying .env.example to .env...
        copy ".env.example" ".env"
        echo.
        echo IMPORTANT: Please edit the .env file with your actual Firebase credentials!
        echo Press any key to open .env file in notepad...
        pause >nul
        notepad .env
    ) else (
        echo ERROR: .env.example file not found!
        echo Please create a .env file with your environment variables.
        pause
        exit /b 1
    )
)

echo.
echo Starting deployment...
echo.

REM Stop any existing containers
echo Stopping existing containers...
docker-compose down

echo.
echo Building and starting services...
docker-compose up --build -d

echo.
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo    Deployment Status
echo ========================================

REM Check if containers are running
docker-compose ps

echo.
echo ========================================
echo    Access Your Application
echo ========================================
echo Frontend: http://localhost:8080
echo Backend:  http://localhost:3000
echo Health:   http://localhost:3000/health
echo.

echo Opening frontend in browser...
start http://localhost:8080

echo.
echo Deployment complete!
echo.
echo To view logs: docker-compose logs
echo To stop:      docker-compose down
echo.
pause
