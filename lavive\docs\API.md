# API Documentation

## Base URL
```
http://localhost:3000/api
```

## Authentication

All API endpoints (except health check) require Firebase ID token authentication.

### Headers
```
Authorization: Bearer <firebase-id-token>
Content-Type: application/json
```

## Endpoints

### Health Check

#### GET /health
Check if the API server is running.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2023-09-18T10:30:00.000Z",
  "service": "Commed Delivery API"
}
```

### Authentication

#### POST /api/auth/verify
Verify Firebase ID token.

**Response:**
```json
{
  "success": true,
  "user": {
    "uid": "user-id",
    "email": "<EMAIL>",
    "name": "User Name"
  },
  "message": "Token is valid"
}
```

#### GET /api/auth/profile
Get user profile information.

**Response:**
```json
{
  "success": true,
  "user": {
    "uid": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "emailVerified": true
  }
}
```

### Orders

#### GET /api/orders
Get all orders with optional filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `status` (string): Filter by status
- `search` (string): Search in customer name, phone, article model, or address

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "order-id",
      "customerName": "John Doe",
      "customerPhone": "+1234567890",
      "deliveryAddress": "123 Main St, City, State",
      "articleModel": "Product ABC",
      "quantity": 2,
      "status": "New",
      "notes": "Special instructions",
      "createdAt": "2023-09-18T10:30:00.000Z",
      "updatedAt": "2023-09-18T10:30:00.000Z",
      "createdBy": "user-id"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20
  }
}
```

#### GET /api/orders/:id
Get a specific order by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "order-id",
    "customerName": "John Doe",
    "customerPhone": "+1234567890",
    "deliveryAddress": "123 Main St, City, State",
    "articleModel": "Product ABC",
    "quantity": 2,
    "status": "New",
    "notes": "Special instructions",
    "createdAt": "2023-09-18T10:30:00.000Z",
    "updatedAt": "2023-09-18T10:30:00.000Z",
    "createdBy": "user-id"
  }
}
```

#### POST /api/orders
Create a new order.

**Request Body:**
```json
{
  "customerName": "John Doe",
  "customerPhone": "+1234567890",
  "deliveryAddress": "123 Main St, City, State",
  "articleModel": "Product ABC",
  "quantity": 2,
  "status": "New",
  "notes": "Special instructions"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "new-order-id",
    "customerName": "John Doe",
    "customerPhone": "+1234567890",
    "deliveryAddress": "123 Main St, City, State",
    "articleModel": "Product ABC",
    "quantity": 2,
    "status": "New",
    "notes": "Special instructions",
    "createdBy": "user-id"
  },
  "message": "Order created successfully"
}
```

#### PUT /api/orders/:id
Update an existing order.

**Request Body:**
```json
{
  "status": "Processing",
  "notes": "Updated instructions"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order updated successfully"
}
```

#### DELETE /api/orders/:id
Soft delete an order.

**Response:**
```json
{
  "success": true,
  "message": "Order deleted successfully"
}
```

## Order Status Values

- `New` - Newly created order
- `Processing` - Order is being processed
- `Shipped` - Order has been shipped
- `Delivered` - Order has been delivered
- `Cancelled` - Order has been cancelled

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": "Missing required fields: customerName, customerPhone"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "No token provided or invalid format"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "Access denied"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Order not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error"
}
```

## Rate Limiting

- 100 requests per 15 minutes per IP address
- Applies to all `/api/*` endpoints

## Data Validation

### Required Fields for Order Creation
- `customerName` (string, non-empty)
- `customerPhone` (string, non-empty)
- `deliveryAddress` (string, non-empty)
- `articleModel` (string, non-empty)
- `quantity` (number, > 0)

### Optional Fields
- `status` (string, must be valid status value)
- `notes` (string)

## Examples

### Create Order with cURL
```bash
curl -X POST http://localhost:3000/api/orders \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "John Doe",
    "customerPhone": "+1234567890",
    "deliveryAddress": "123 Main St, City, State",
    "articleModel": "Product ABC",
    "quantity": 2,
    "status": "New",
    "notes": "Handle with care"
  }'
```

### Get Orders with Filtering
```bash
curl "http://localhost:3000/api/orders?status=New&search=john&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN"
```
