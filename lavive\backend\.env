# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:8080

# Firebase Configuration - Backend Service Account
# IMPORTANT: You need to generate a Service Account key from Firebase Console
# Go to: Firebase Console > Project Settings > Service Accounts > Generate new private key
# Then replace the placeholder values below with the actual values from the downloaded JSON file

FIREBASE_PROJECT_ID=lavive-cb219
FIREBASE_PRIVATE_KEY_ID=19b7582ae25b053da5b33b83288ad86daa9d4244
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=100489032623938359419
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40lavive-cb219.iam.gserviceaccount.com

# Security
JWT_SECRET=your-super-secret-jwt-key-here-change-this-in-production
