@echo off
echo ========================================
echo    GitHub Repository Setup
echo ========================================
echo.

REM Check if git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Git is not installed!
    echo Please install Git from: https://git-scm.com/download/win
    echo.
    start https://git-scm.com/download/win
    pause
    exit /b 1
)

echo Git is installed!
echo.

REM Check if already a git repository
if exist ".git" (
    echo This directory is already a Git repository.
    echo Current remote:
    git remote -v
    echo.
    set /p continue="Do you want to continue anyway? (y/n): "
    if /i not "%continue%"=="y" exit /b 0
)

echo Setting up Git repository...
echo.

REM Get user input
set /p repo_name="Enter repository name (e.g., lavive-delivery): "
set /p github_username="Enter your GitHub username: "

if "%repo_name%"=="" set repo_name=lavive-delivery
if "%github_username%"=="" (
    echo Error: GitHub username is required
    pause
    exit /b 1
)

echo.
echo Repository: %repo_name%
echo Username: %github_username%
echo GitHub URL: https://github.com/%github_username%/%repo_name%
echo.

set /p confirm="Is this correct? (y/n): "
if /i not "%confirm%"=="y" (
    echo Setup cancelled.
    pause
    exit /b 0
)

echo.
echo Initializing Git repository...

REM Initialize git if not already done
if not exist ".git" (
    git init
    echo Git repository initialized.
)

REM Add all files
echo Adding files to Git...
git add .

REM Commit
echo Committing files...
git commit -m "Initial commit - Lavive delivery management system"

REM Set main branch
git branch -M main

REM Add remote
echo Adding GitHub remote...
git remote remove origin 2>nul
git remote add origin https://github.com/%github_username%/%repo_name%.git

echo.
echo ========================================
echo    Next Steps
echo ========================================
echo.
echo 1. Create the repository on GitHub:
echo    - Go to: https://github.com/new
echo    - Repository name: %repo_name%
echo    - Make it PUBLIC (for free Railway deployment)
echo    - Don't initialize with README (we already have files)
echo    - Click "Create repository"
echo.
echo 2. Push your code:
echo    git push -u origin main
echo.
echo 3. Then you can deploy to Railway:
echo    - Go to https://railway.app
echo    - Sign up with GitHub
echo    - Deploy from GitHub repo
echo.

echo Opening GitHub to create repository...
start https://github.com/new

echo.
echo Repository setup complete!
echo Run this command to push your code:
echo    git push -u origin main
echo.
pause
