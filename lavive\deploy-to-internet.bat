@echo off
echo ========================================
echo    Lavive Internet Deployment Helper
echo ========================================
echo.

echo Choose your deployment platform:
echo.
echo 1. Railway (Recommended - Easy)
echo 2. Firebase Hosting + Cloud Run
echo 3. Heroku
echo 4. DigitalOcean App Platform
echo 5. Manual Setup Guide
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto railway
if "%choice%"=="2" goto firebase
if "%choice%"=="3" goto heroku
if "%choice%"=="4" goto digitalocean
if "%choice%"=="5" goto manual
goto invalid

:railway
echo.
echo ========================================
echo    Railway Deployment
echo ========================================
echo.
echo Steps to deploy to Railway:
echo.
echo 1. Create a GitHub repository for your project
echo 2. Push your code to GitHub:
echo    git init
echo    git add .
echo    git commit -m "Initial commit"
echo    git remote add origin https://github.com/yourusername/lavive.git
echo    git push -u origin main
echo.
echo 3. Go to https://railway.app
echo 4. Sign up/Login with GitHub
echo 5. Click "New Project" → "Deploy from GitHub repo"
echo 6. Select your repository
echo 7. Deploy backend: Select backend folder
echo 8. Deploy frontend: Create new service, select frontend folder
echo 9. Set environment variables in Railway dashboard
echo.
echo Your app will be available at: https://your-app.railway.app
echo.
start https://railway.app
goto end

:firebase
echo.
echo ========================================
echo    Firebase + Cloud Run Deployment
echo ========================================
echo.
echo Steps:
echo 1. Install Google Cloud CLI
echo 2. Run: gcloud auth login
echo 3. Deploy backend to Cloud Run
echo 4. Deploy frontend to Firebase Hosting
echo.
echo Opening Google Cloud Console...
start https://console.cloud.google.com
goto end

:heroku
echo.
echo ========================================
echo    Heroku Deployment
echo ========================================
echo.
echo Steps:
echo 1. Install Heroku CLI
echo 2. heroku login
echo 3. heroku create lavive-backend
echo 4. heroku create lavive-frontend
echo 5. Deploy using Git
echo.
echo Opening Heroku...
start https://heroku.com
goto end

:digitalocean
echo.
echo ========================================
echo    DigitalOcean App Platform
echo ========================================
echo.
echo Steps:
echo 1. Create DigitalOcean account
echo 2. Go to App Platform
echo 3. Connect GitHub repository
echo 4. Configure Docker build
echo.
echo Opening DigitalOcean...
start https://cloud.digitalocean.com/apps
goto end

:manual
echo.
echo ========================================
echo    Manual Setup Guide
echo ========================================
echo.
echo Opening the detailed deployment guide...
start INTERNET_DEPLOYMENT_GUIDE.md
goto end

:invalid
echo Invalid choice. Please run the script again.
goto end

:end
echo.
echo Need help? Check INTERNET_DEPLOYMENT_GUIDE.md
echo.
pause
